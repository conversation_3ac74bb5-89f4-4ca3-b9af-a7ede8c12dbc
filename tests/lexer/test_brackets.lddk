block test_matched{
    block input : _lexer_input!
    ========
    (a + b) * [c - d] + {e / f}
    ========

    block result : _lexer_expected!
    ========
    +LeftParen+
    (
    +Identifier+
    a
    +BinaryOperator+
    +
    +Identifier+
    b
    +RightParen+
    )
    +BinaryOperator+
    *
    +LeftBracket+
    [
    +Identifier+
    c
    +BinaryOperator+
    -
    +Identifier+
    d
    +RightBracket+
    ]
    +BinaryOperator+
    +
    +LeftBrace+
    {
    +Identifier+
    e
    +BinaryOperator+
    /
    +Identifier+
    f
    +RightBrace+
    }
    ========
}

block test_bracket_errors
{
    block test_bracket_errors : _lexer_input!
    ========
    (a + b]
    ========

    block test_bracket_errors_expected : _lexer_error_state!
    ========
    1:7
    nest_error
    ========

}

block test_unclosed_bracket
{
    block test_unclosed_bracket : _lexer_input!
    ========
    (a + b
    ========

    block test_unclosed_bracket_expected : _lexer_error_state!
    ========
    1:7
    nest_error
    ========
}

block test_extra_close_bracket
{
    block test_extra_close_bracket : _lexer_input!
    ========
    a + b)
    ========

    block test_extra_close_bracket_expected : _lexer_error_state!
    ========
    1:6
    nest_error
    ========
}