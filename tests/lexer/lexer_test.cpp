#include <fstream>
#include <iostream>
#include <string>
#include <map>
#include <list>
#include <cassert>
#include <locale>
#include <filesystem>
#include <unicode/urename.h>
#include "../../src/llvm-frontend/text.h"
#include "../../src/llvm-frontend/lexer.h"


std::string u32string_to_string(const std::u32string& u32str) {
    char* utf8 = NULL;
    int len = Text::char32ToUtf8(u32str.c_str(), u32str.size(), &utf8);
    if (len <= 0) return "";
    std::string res(utf8);
    free(utf8);
    return res;
}

// 全局错误收集器
LexerErrorCollector g_error_collector;

void LexerErrorCollector::add_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    int line = info ? info->line : 0;
    int column = info ? info->column : 0;
    errors.emplace_back(type, message, line, column);
}

void LexerErrorCollector::add_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    int line = info ? info->line : 0;
    int column = info ? info->column : 0;
    warnings.emplace_back(type, message, line, column);
}

void generate_error(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Error at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Error: %s\n", message_str.c_str());
    }
}

void generate_warning(const std::u32string& message, const SoueceInfo* info) {
    std::string message_str = u32string_to_string(message);
    if (info) {
        printf("Warning at line %d, column %d: %s\n", info->line, info->column, message_str.c_str());
    }else{
        printf("Warning: %s\n", message_str.c_str());
    }
}

void generate_structured_error(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    g_error_collector.add_error(type, message, info);
    generate_error(message, info);
}

void generate_structured_warning(LexerErrorType type, const std::u32string& message, const SoueceInfo* info) {
    g_error_collector.add_warning(type, message, info);
    generate_warning(message, info);
}

struct ErrorStateExpectation {
    int line;
    int column;
    std::vector<LexerErrorType> error_types;
};

struct TestData {
    std::u32string input;
    std::vector<std::u32string> expected_tokens;
    std::vector<TokenType> expected_types;
    std::vector<ErrorStateExpectation> expected_errors;
};

static std::vector<std::u32string> split_by(const std::u32string& input, char32_t split_char) {
    std::vector<std::u32string> res;
    std::u32string current;
    for (auto c : input) {
        if (c == split_char) {
            res.push_back(current);
            current.clear();
        }else{
            current += c;
        }
    }
    if (current.size() > 0) {
        res.push_back(current);
    }
    return res;
}

static TokenType token_type_from_string(const std::u32string& type_string) {
    if (type_string == U"Identifier") return TokenType::Identifier;
    if (type_string == U"RawIdentifier") return TokenType::RawIdentifier;
    if (type_string == U"Number") return TokenType::Number;
    if (type_string == U"String") return TokenType::String;
    if (type_string == U"Char") return TokenType::Char;
    if (type_string == U"LineComment") return TokenType::LineComment;
    if (type_string == U"BlockComment") return TokenType::BlockComment;
    if (type_string == U"BinaryOperator") return TokenType::BinaryOperator;
    if (type_string == U"PrefixOperator") return TokenType::PrefixOperator;
    if (type_string == U"SuffixOperator") return TokenType::SuffixOperator;
    if (type_string == U"Keyword") return TokenType::Keyword;
    if (type_string == U"Punctuation") return TokenType::Punctuation;
    if (type_string == U"Whitespace") return TokenType::Whitespace;
    if (type_string == U"BlockContent") return TokenType::BlockContent;
    if (type_string == U"LeftParen") return TokenType::LeftParen;
    if (type_string == U"RightParen") return TokenType::RightParen;
    if (type_string == U"LeftBracket") return TokenType::LeftBracket;
    if (type_string == U"RightBracket") return TokenType::RightBracket;
    if (type_string == U"LeftBrace") return TokenType::LeftBrace;
    if (type_string == U"RightBrace") return TokenType::RightBrace;
    return TokenType::Identifier;
}

static std::u32string token_type_to_string(TokenType type) {
    switch (type) {
        case TokenType::Identifier: return U"Identifier";
        case TokenType::RawIdentifier: return U"RawIdentifier";
        case TokenType::Number: return U"Number";
        case TokenType::String: return U"String";
        case TokenType::Char: return U"Char";
        case TokenType::LineComment: return U"LineComment";
        case TokenType::BlockComment: return U"BlockComment";
        case TokenType::BinaryOperator: return U"BinaryOperator";
        case TokenType::PrefixOperator: return U"PrefixOperator";
        case TokenType::SuffixOperator: return U"SuffixOperator";
        case TokenType::Keyword: return U"Keyword";
        case TokenType::Punctuation: return U"Punctuation";
        case TokenType::Whitespace: return U"Whitespace";
        case TokenType::BlockContent: return U"BlockContent";
        case TokenType::LeftParen: return U"LeftParen";
        case TokenType::RightParen: return U"RightParen";
        case TokenType::LeftBracket: return U"LeftBracket";
        case TokenType::RightBracket: return U"RightBracket";
        case TokenType::LeftBrace: return U"LeftBrace";
        case TokenType::RightBrace: return U"RightBrace";
    }
    return U"Unknown";
}

static LexerErrorType error_type_from_string(const std::u32string& error_string) {
    if (error_string == U"unexpected_character") return LexerErrorType::UnexpectedCharacter;
    if (error_string == U"lexer_inject") return LexerErrorType::LexerInject;
    if (error_string == U"invalid_escape_sequence") return LexerErrorType::InvalidEscapeSequence;
    if (error_string == U"char_must_be_one_character") return LexerErrorType::CharMustBeOneCharacter;
    if (error_string == U"raw_identifier_must_be_in_one_line") return LexerErrorType::RawIdentifierMustBeInOneLine;
    if (error_string == U"enhanced_block_must_start_with_newline") return LexerErrorType::EnhancedBlockMustStartWithNewline;
    if (error_string == U"enhanced_block_delimiter_must_be_3_to_120_characters") return LexerErrorType::EnhancedBlockDelimiterMustBe3To120Characters;
    if (error_string == U"enhanced_block_delimiter_must_consist_of_dash_or_equal") return LexerErrorType::EnhancedBlockDelimiterMustConsistOfDashOrEqual;
    if (error_string == U"enhanced_block_indent_mismatch") return LexerErrorType::EnhancedBlockIndentMismatch;
    if (error_string == U"enhanced_block_indent_must_be_spaces") return LexerErrorType::EnhancedBlockIndentMustBeSpaces;
    if (error_string == U"unexpected_enhanced_block_state") return LexerErrorType::UnexpectedEnhancedBlockState;
    if (error_string == U"unexpected_state") return LexerErrorType::UnexpectedState;
    if (error_string == U"nest_error") return LexerErrorType::NestError;
    if (error_string == U"tokenization_rejected_by_all_states") return LexerErrorType::TokenizationRejectedByAllStates;
    if (error_string == U"tokenization_accepted_by_multiple_states") return LexerErrorType::TokenizationAcceptedByMultipleStates;
    if (error_string == U"multiple_states_after_finalization") return LexerErrorType::MultipleStatesAfterFinalization;
    if (error_string == U"no_state_after_finalization") return LexerErrorType::NoStateAfterFinalization;
    if (error_string == U"not_in_normal_state_after_finalization") return LexerErrorType::NotInNormalStateAfterFinalization;
    return LexerErrorType::UnexpectedState;
}

void parse_expected_tokens(std::vector<std::u32string>& out_tokens,std::vector<TokenType>& out_types, const std::u32string& input) {
    auto lines = split_by(input, '\n');
    enum class ReadState{
        WaitType, WaitValue
    } read_state = ReadState::WaitType;
    std::u32string token_type_string, token_value_string;
    for(auto const& line : lines) {
        if (read_state == ReadState::WaitType) {
            if (line.size() <= 2) continue;
            if (line.front() != U'+' || line.back() != U'+') continue;
            token_type_string = line.substr(1, line.size() - 2);
            read_state = ReadState::WaitValue;
        }else if (read_state == ReadState::WaitValue) {
            if (line.size() <= 2) {
                token_value_string += line;
                continue;
            }else if (line.front() == U'+' && line.back() == U'+') {
                out_tokens.push_back(token_value_string);
                token_value_string.clear();
                out_types.push_back(token_type_from_string(token_type_string));
                token_type_string = line.substr(1, line.size() - 2);
                read_state = ReadState::WaitValue;
            }else{
                token_value_string += line;
            }
        }
    }
    if (token_value_string.size() > 0) {
        out_tokens.push_back(token_value_string);
        out_types.push_back(token_type_from_string(token_type_string));
    }
}

void parse_expected_errors(std::vector<ErrorStateExpectation>& out_errors, const std::u32string& input) {
    auto lines = split_by(input, '\n');
    ErrorStateExpectation current_error;
    bool reading_position = true;

    for(auto const& line : lines) {
        if (line.empty()) continue;

        // 检查是否是新的位置行（格式：行号:列号）
        auto colon_pos = line.find(U':');
        if (colon_pos != std::u32string::npos) {
            // 如果之前有未完成的错误，先保存它
            if (!reading_position && !current_error.error_types.empty()) {
                out_errors.push_back(current_error);
            }

            // 开始新的错误位置
            std::u32string line_str = line.substr(0, colon_pos);
            std::u32string col_str = line.substr(colon_pos + 1);

            // 转换为整数
            current_error.line = std::stoi(u32string_to_string(line_str));
            current_error.column = std::stoi(u32string_to_string(col_str));
            current_error.error_types.clear();
            reading_position = false;
        } else if (!reading_position) {
            // 解析错误类型
            if (!line.empty()) {
                current_error.error_types.push_back(error_type_from_string(line));
            }
        }
    }

    // 保存最后一个错误
    if (!reading_position && !current_error.error_types.empty()) {
        out_errors.push_back(current_error);
    }
}

std::map<std::u32string, TestData> read_test_data(const LexerState& lexer) {
    auto tokens = lexer.get_tokens();
    std::map<std::u32string, TestData> res;

    if (tokens.size() <= 1) return res;

    std::u32string current_test_name;

    // 寻找块结构：block <name> : <type>! 或 block <name> { ... }
    for (auto it = tokens.begin(); it != tokens.end(); it++) {
        std::shared_ptr<Token> token = *it;
        if (token->type == TokenType::Identifier && token->value == U"block") {
            // 找到block关键字，寻找后续的结构
            auto name_it = std::next(it);
            // 确保为大括号
            auto brace_it = std::next(name_it);
            if (brace_it != tokens.end() && (*brace_it)->type == TokenType::LeftBrace) {
                if (name_it != tokens.end() && (*name_it)->type == TokenType::Identifier) {
                    current_test_name = (*name_it)->value;
                }
            }
        }else if (token->type == TokenType::BinaryOperator && token->value == U":" && token->nest_state.length() == 1) {
            // 寻找块类型
            auto type_it = std::next(it);
            if (type_it != tokens.end() && (*type_it)->type == TokenType::Identifier) {
                std::u32string block_type = (*type_it)->value;

                // 寻找块内容
                auto content_it = std::next(type_it);

                if (content_it != tokens.end() && (*content_it)->type == TokenType::BlockContent) {
                    std::u32string content = (*content_it)->value;

                    if (block_type == U"_lexer_input") {
                        res[current_test_name].input = content;
                    }else if (block_type == U"_lexer_expected") {
                        parse_expected_tokens(res[current_test_name].expected_tokens, res[current_test_name].expected_types, content);
                    }else if (block_type == U"_lexer_error_state") {
                        parse_expected_errors(res[current_test_name].expected_errors, content);
                    }else if (block_type == U"text") {
                        // 忽略文档块
                    }

                    it = content_it; // 跳过已处理的token
                }
            }
        }else if (token->type == TokenType::RightBrace) {
            // 块结束
            if (res[current_test_name].expected_tokens.empty() && res[current_test_name].expected_errors.empty())
            {
                generate_warning(U"Empty test_result/error block: " + current_test_name, nullptr);
            }
            if (res[current_test_name].input.empty()){
                generate_error(U"Empty test_input block: " + current_test_name, nullptr);
            }
            current_test_name.clear();
        }
    }

    return res;
}

bool run_single_test(const TestData& test_data) {
    // 清空错误收集器
    g_error_collector.clear();



    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : test_data.input) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);
    auto tokens = lexer.get_tokens();

    bool has_error = false;

    // 验证token输出（如果有期望的token）
    if (!test_data.expected_tokens.empty()) {
        auto output_it = tokens.begin();
        auto expected_value_it = test_data.expected_tokens.begin();
        auto expected_type_it = test_data.expected_types.begin();

        while (output_it != tokens.end() && expected_value_it != test_data.expected_tokens.end() && expected_type_it != test_data.expected_types.end()) {
            if ((*output_it)->value != *expected_value_it) {
                generate_error(U"Expected token value: " + *expected_value_it + U", but got: " + (*output_it)->value, nullptr);
                has_error = true;
                break;
            }
            if ((*output_it)->type != *expected_type_it) {
                generate_error(U"Expected token type: " + token_type_to_string(*expected_type_it) + U", but got: " + token_type_to_string((*output_it)->type), nullptr);
                has_error = true;
                break;
            }
            output_it++;
            expected_value_it++;
            expected_type_it++;
        }
        if (output_it != tokens.end()) {
            generate_error(U"Extra tokens: " + (*output_it)->value, nullptr);
            has_error = true;
        }
        if (expected_value_it != test_data.expected_tokens.end()) {
            generate_error(U"Missing tokens: " + *expected_value_it, nullptr);
            has_error = true;
        }
    }else if (test_data.expected_errors.empty()){
        // 仅有输入，打印输出
        std::cout << "Input: \"" << u32string_to_string(test_data.input) << "\"" << std::endl;
        std::cout << "\nGenerated Tokens:" << std::endl;
        if (tokens.empty()) {
            std::cout << "    (no tokens)" << std::endl;
        } else {
            for (auto& token : tokens) {
                std::cout << "+" << u32string_to_string(token_type_to_string(token->type)) << "+" << std::endl;
                std::cout << u32string_to_string(token->value) << std::endl;
            }
        }
        if (!g_error_collector.errors.empty()) {
            std::cout << "\nErrors:" << std::endl;
            for (const auto& error : g_error_collector.errors) {
                std::cout << "    " << error.line << ":" << error.column << " - " << u32string_to_string(error.message) << std::endl;
            }
        }
    }

    // 验证错误状态（如果有期望的错误）
    if (!test_data.expected_errors.empty()) {
        for (const auto& expected_error : test_data.expected_errors) {
            bool found_matching_error = false;
            for (const auto& actual_error : g_error_collector.errors) {
                if (actual_error.line == expected_error.line && actual_error.column == expected_error.column) {
                    // 检查是否包含期望的错误类型
                    for (const auto& expected_type : expected_error.error_types) {
                        if (actual_error.type == expected_type) {
                            found_matching_error = true;
                            break;
                        }
                    }
                    if (found_matching_error) break;
                }
            }
            if (!found_matching_error) {
                std::cout << "Expected error at " << expected_error.line << ":" << expected_error.column << " not found" << std::endl;
                has_error = true;
            }
        }
    }

    if (has_error) {
        std::cout << "Input: \"" << u32string_to_string(test_data.input) << "\"" << std::endl;

        std::cout << "\nActual Output:" << std::endl;
        if (tokens.empty()) {
            std::cout << "    (no tokens)" << std::endl;
        } else {
            for (auto& token : tokens) {
                std::cout << "    " << u32string_to_string(token->value) << " : " << u32string_to_string(token_type_to_string(token->type));
                if (!token->nest_state.empty()) {
                    std::cout << " [nest: " << u32string_to_string(token->nest_state) << "]";
                }
                std::cout << std::endl;
            }
        }

        if (!test_data.expected_tokens.empty()) {
            std::cout << "\nExpected Output:" << std::endl;
            for (size_t i = 0; i < test_data.expected_tokens.size() && i < test_data.expected_types.size(); i++) {
                std::cout << "    " << u32string_to_string(test_data.expected_tokens[i]) << " : " << u32string_to_string(token_type_to_string(test_data.expected_types[i])) << std::endl;
            }
        }

        if (!g_error_collector.errors.empty()) {
            std::cout << "\nActual Errors:" << std::endl;
            for (const auto& error : g_error_collector.errors) {
                std::cout << "    " << error.line << ":" << error.column << " - " << u32string_to_string(error.message) << std::endl;
            }
        }

        if (!test_data.expected_errors.empty()) {
            std::cout << "\nExpected Errors:" << std::endl;
            for (const auto& expected_error : test_data.expected_errors) {
                std::cout << "    " << expected_error.line << ":" << expected_error.column << " - ";
                for (size_t i = 0; i < expected_error.error_types.size(); i++) {
                    if (i > 0) std::cout << ", ";
                    // 这里可以添加错误类型到字符串的转换
                    std::cout << "error_type_" << static_cast<int>(expected_error.error_types[i]);
                }
                std::cout << std::endl;
            }
        }
    }
    return !has_error;
}

bool run_tests(std::map<std::u32string, TestData>& test_data) {
    bool res = true;
    int test_count = 0;
    int passed_count = 0;

    std::cout << "\n========================================" << std::endl;
    std::cout << "Running " << test_data.size() << " tests..." << std::endl;
    std::cout << "========================================" << std::endl;

    for (auto& data : test_data) {
        test_count++;
        std::cout << "\n[" << test_count << "/" << test_data.size() << "] Test: " << u32string_to_string(data.first) << std::endl;
        std::cout << "----------------------------------------" << std::endl;

        bool test_passed = run_single_test(data.second);
        if (test_passed) {
            passed_count++;
            std::cout << "✓ PASSED" << std::endl;
        } else {
            std::cout << "✗ FAILED" << std::endl;
        }
        res &= test_passed;
    }

    std::cout << "\n========================================" << std::endl;
    std::cout << "Test Summary: " << passed_count << "/" << test_count << " passed" << std::endl;
    std::cout << "========================================" << std::endl;

    return res;
}

static int read_program_from_file(std::u32string& out_program, const std::string& filename) {
    std::ifstream file(filename);
    std::cout << "Reading file: " << filename << std::endl;
    if (!file.is_open()) {
        std::cout << "Failed to open test file" << std::endl;
        return -1;
    }
    std::string content((std::istreambuf_iterator<char>(file)),
        std::istreambuf_iterator<char>());
    char32_t *u32 = NULL;
    int len = Text::utf8ToChar32(content.c_str(), content.size(), &u32);
    if (len <= 0)
    {
        std::cout << "Failed to convert to UTF-32" << std::endl;
        return -1;
    }
    out_program = std::u32string(u32, u32 + len - 1);
    free(u32);
    return 0;
}

bool run_test_file(const std::string& filename) {
    std::u32string program;
    if (read_program_from_file(program, filename) != 0) {
        std::cout << "Read test file failed: " << filename << std::endl;
        return false;
    }

    LexerState lexer;
    SoueceInfo info{1, 1};
    for (auto c : program) {
        lexer = lexer.eat_char(c, &info);
        info.column++;
        if (c == '\n') {
            info.line++;
            info.column = 1;
        }
    }
    lexer = lexer.finalize(&info);

    auto test_data = read_test_data(lexer);
    if (test_data.empty()) {
        std::cout << "No tests found" << std::endl;
        return false;
    }

    return run_tests(test_data);
}

std::vector<std::string> search_test_files(const std::string& path) {
    std::vector<std::string> res;
    for (const auto& entry : std::filesystem::directory_iterator(path)) {
        if (entry.is_regular_file() && entry.path().extension() == ".lddk") {
            res.push_back(entry.path().string());
        }
    }
    return res;
}

int main(int argc, char** argv) {
    setlocale(LC_ALL, "zh_CN.utf8");
    std::string work_path = std::filesystem::current_path().string();
    std::filesystem::path test_path = work_path + "/tests/lexer";
    std::vector<std::string> test_files;
    if (argc > 1) {
        while (argc-- > 1) {
            test_files.push_back(argv[argc]);
        }
    }else{
        test_files = search_test_files(test_path.string());
    }
    bool all_passed = true;
    for (auto& file : test_files) {
        all_passed &= run_test_file(file);
    }
    if (all_passed) {
        std::cout << "All tests passed" << std::endl;
    }else{
        std::cout << "Some tests failed" << std::endl;
    }
    return 0;
}
