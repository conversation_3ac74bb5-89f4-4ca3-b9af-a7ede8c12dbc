block test_nested_correct {
    block input : _lexer_input !
    ========
    ((a + b) * [c - {d / e}])
    ========

    block output : _lexer_expected !
    ==============================
    +LeftParen+
    (
    +LeftParen+
    (
    +Identifier+
    a
    +BinaryOperator+
    +
    +Identifier+
    b
    +RightParen+
    )
    +BinaryOperator+
    *
    +LeftBracket+
    [
    +Identifier+
    c
    +BinaryOperator+
    -
    +LeftBrace+
    {
    +Identifier+
    d
    +BinaryOperator+
    /
    +Identifier+
    e
    +RightBrace+
    }
    +RightBracket+
    ]
    +RightParen+
    )
    ==============================

}

block test_nested_wrong {
    block input : _lexer_input !
    ========
    ((a + b) * [c - {d / e}]
    ========

    block error : _lexer_error_state !
    ========
    1:25
    nest_error
    ========
}

block test_complex_mismatch {
    block input : _lexer_input !
    ========
    ((a + b] * [c - d})
    ========

    block error : _lexer_error_state !
    ========
    1:8
    nest_error
    1:18
    nest_error
    1:19
    nest_error
    ========
}
