#include <iostream>
#include <cassert>
#include "../../src/llvm-frontend/node.h"

using namespace lddk;
using namespace lddk::literals;

void test_basic_types() {
    std::cout << "Testing basic types..." << std::endl;
    
    // 测试基本类型
    auto null_node = Node::make_null();
    auto bool_node = Node::make_bool(true);
    auto int_node = Node::make_int(42);
    auto float_node = Node::make_float(3.14);
    auto string_node = Node::make_string("hello");
    
    assert(null_node->is_null());
    assert(bool_node->is_bool() && bool_node->as_bool() == true);
    assert(int_node->is_int() && int_node->as_int() == 42);
    assert(float_node->is_float() && float_node->as_float() == 3.14);
    assert(string_node->is_string() && string_node->as_string() == "hello");
    
    std::cout << "✓ Basic types test passed" << std::endl;
}

void test_array_operations() {
    std::cout << "Testing array operations..." << std::endl;
    
    auto array = Node::make_array();
    assert(array->is_array());
    assert(array->size() == 0);
    
    // 添加元素
    array->push_back(Node::make_int(1));
    array->push_back(Node::make_int(2));
    array->push_back(Node::make_int(3));
    
    assert(array->size() == 3);
    assert((*array)[0]->as_int() == 1);
    assert((*array)[1]->as_int() == 2);
    assert((*array)[2]->as_int() == 3);
    
    // 测试索引访问
    (*array)[1] = Node::make_string("modified");
    assert((*array)[1]->is_string());
    assert((*array)[1]->as_string() == "modified");
    
    std::cout << "✓ Array operations test passed" << std::endl;
}

void test_object_operations() {
    std::cout << "Testing object operations..." << std::endl;
    
    auto obj = Node::make_object();
    assert(obj->is_object());
    assert(obj->size() == 0);
    
    // 添加键值对
    (*obj)["name"] = Node::make_string("John");
    (*obj)["age"] = Node::make_int(30);
    (*obj)["active"] = Node::make_bool(true);
    
    assert(obj->size() == 3);
    assert(obj->contains("name"));
    assert(obj->contains("age"));
    assert(obj->contains("active"));
    assert(!obj->contains("nonexistent"));
    
    assert((*obj)["name"]->as_string() == "John");
    assert((*obj)["age"]->as_int() == 30);
    assert((*obj)["active"]->as_bool() == true);
    
    // 测试删除
    obj->erase("age");
    assert(obj->size() == 2);
    assert(!obj->contains("age"));
    
    std::cout << "✓ Object operations test passed" << std::endl;
}

void test_hash_stability() {
    std::cout << "Testing hash stability..." << std::endl;
    
    // 创建相同内容的节点
    auto node1 = Node::make_string("test");
    auto node2 = Node::make_string("test");
    
    // 哈希应该相同
    assert(node1->hash() == node2->hash());
    
    // 创建复杂对象
    auto obj1 = Node::make_object();
    (*obj1)["a"] = Node::make_int(1);
    (*obj1)["b"] = Node::make_string("hello");
    
    auto obj2 = Node::make_object();
    (*obj2)["a"] = Node::make_int(1);
    (*obj2)["b"] = Node::make_string("hello");
    
    // 相同内容的对象应该有相同的哈希
    assert(obj1->hash() == obj2->hash());
    
    // 修改一个对象
    (*obj2)["c"] = Node::make_bool(true);
    
    // 哈希应该不同
    assert(obj1->hash() != obj2->hash());
    
    std::cout << "✓ Hash stability test passed" << std::endl;
}

void test_nested_structures() {
    std::cout << "Testing nested structures..." << std::endl;
    
    // 创建嵌套结构
    auto root = Node::make_object();
    (*root)["metadata"] = Node::make_object();
    (*(*root)["metadata"])["version"] = Node::make_string("1.0");
    (*(*root)["metadata"])["author"] = Node::make_string("LDDK");
    
    (*root)["data"] = Node::make_array();
    (*(*root)["data"]).push_back(Node::make_int(1));
    (*(*root)["data"]).push_back(Node::make_int(2));
    (*(*root)["data"]).push_back(Node::make_int(3));
    
    // 验证嵌套访问
    assert((*(*root)["metadata"])["version"]->as_string() == "1.0");
    assert((*(*root)["metadata"])["author"]->as_string() == "LDDK");
    assert((*(*root)["data"]).size() == 3);
    assert((*(*root)["data"])[1]->as_int() == 2);
    
    std::cout << "✓ Nested structures test passed" << std::endl;
}

void test_literals() {
    std::cout << "Testing literals..." << std::endl;

    auto str_node = "hello"_node;
    auto int_node = 42_node;
    auto float_node = 3.14_node;

    assert(str_node->is_string() && str_node->as_string() == "hello");
    assert(int_node->is_int() && int_node->as_int() == 42);
    assert(float_node->is_float() && float_node->as_float() == 3.14);

    std::cout << "✓ Literals test passed" << std::endl;
}

void test_node_store() {
    std::cout << "Testing NodeStore..." << std::endl;

    auto& store = NodeStore::instance();

    // 创建一些节点
    auto node1 = Node::make_string("test1");
    auto node2 = Node::make_int(42);

    // 存储节点
    NodeHash hash1 = store.store(node1);
    NodeHash hash2 = store.store(node2);

    // 验证存储
    assert(store.contains(hash1));
    assert(store.contains(hash2));

    // 检索节点
    auto retrieved1 = store.retrieve(hash1);
    auto retrieved2 = store.retrieve(hash2);

    assert(retrieved1 && retrieved1->is_string() && retrieved1->as_string() == "test1");
    assert(retrieved2 && retrieved2->is_int() && retrieved2->as_int() == 42);

    std::cout << "✓ NodeStore test passed" << std::endl;
}

void test_serialization() {
    std::cout << "Testing serialization..." << std::endl;

    // 创建复杂的嵌套结构
    auto root = Node::make_object();
    (*root)["name"] = Node::make_string("LDDK");
    (*root)["version"] = Node::make_int(1);
    (*root)["active"] = Node::make_bool(true);

    auto features = Node::make_array();
    features->push_back(Node::make_string("lexer"));
    features->push_back(Node::make_string("parser"));
    features->push_back(Node::make_string("codegen"));
    (*root)["features"] = features;

    // 序列化
    std::string serialized = root->to_string();
    std::cout << "Serialized structure:\n" << serialized << std::endl;

    // 验证序列化包含预期内容
    assert(serialized.find("\"name\": \"LDDK\"") != std::string::npos);
    assert(serialized.find("\"version\": 1") != std::string::npos);
    assert(serialized.find("\"active\": true") != std::string::npos);
    assert(serialized.find("\"lexer\"") != std::string::npos);

    std::cout << "✓ Serialization test passed" << std::endl;
}

int main() {
    std::cout << "Running Node data structure tests..." << std::endl;
    std::cout << "====================================" << std::endl;
    
    try {
        test_basic_types();
        test_array_operations();
        test_object_operations();
        test_hash_stability();
        test_nested_structures();
        test_literals();
        test_node_store();
        test_serialization();
        
        std::cout << "\n====================================" << std::endl;
        std::cout << "All tests passed! ✓" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\nTest failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "\nTest failed with unknown exception" << std::endl;
        return 1;
    }
}
