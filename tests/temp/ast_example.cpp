#include <iostream>
#include "../../src/llvm-frontend/node.h"

using namespace lddk;
using namespace lddk::literals;

// AST节点类型常量
namespace ast_types {
    const std::string PROGRAM = "program";
    const std::string FUNCTION = "function";
    const std::string BLOCK = "block";
    const std::string IDENTIFIER = "identifier";
    const std::string LITERAL = "literal";
    const std::string BINARY_OP = "binary_op";
    const std::string CALL = "call";
}

// 便利函数：创建AST节点
NodePtr create_ast_node(const std::string& type) {
    auto node = Node::make_object();
    (*node)["type"] = Node::make_string(type);
    (*node)["children"] = Node::make_array();
    return node;
}

NodePtr create_identifier(const std::string& name) {
    auto node = create_ast_node(ast_types::IDENTIFIER);
    (*node)["name"] = Node::make_string(name);
    return node;
}


NodePtr create_literal(int value) {
    auto node = create_ast_node(ast_types::LITERAL);
    (*node)["value"] = Node::make_int(value);
    (*node)["literal_type"] = Node::make_string("int");
    return node;
}

NodePtr create_binary_op(const std::string& op, NodePtr left, NodePtr right) {
    auto node = create_ast_node(ast_types::BINARY_OP);
    (*node)["operator"] = Node::make_string(op);
    (*(*node)["children"]).push_back(left);
    (*(*node)["children"]).push_back(right);
    return node;
}

NodePtr create_function(const std::string& name, NodePtr body) {
    auto node = create_ast_node(ast_types::FUNCTION);
    (*node)["name"] = Node::make_string(name);
    (*node)["parameters"] = Node::make_array();
    (*(*node)["children"]).push_back(body);
    return node;
}

NodePtr create_call(const std::string& function_name, const std::vector<NodePtr>& args) {
    auto node = create_ast_node(ast_types::CALL);
    (*node)["function"] = Node::make_string(function_name);
    for (const auto& arg : args) {
        (*(*node)["children"]).push_back(arg);
    }
    return node;
}

// 示例：构建一个简单的AST
// 表示代码：add(2 + 3, 4)
NodePtr build_example_ast() {
    // 创建 2 + 3
    auto literal_2 = create_literal(2);
    auto literal_3 = create_literal(3);
    auto add_expr = create_binary_op("+", literal_2, literal_3);
    
    // 创建 4
    auto literal_4 = create_literal(4);
    
    // 创建函数调用 add(2 + 3, 4)
    auto call_node = create_call("add", {add_expr, literal_4});
    
    return call_node;
}

// 示例：使用内容寻址存储AST节点
void demonstrate_content_addressing() {
    std::cout << "\n=== Content Addressing Demo ===" << std::endl;
    
    auto& store = NodeStore::instance();
    
    // 创建相同的AST两次
    auto ast1 = build_example_ast();
    auto ast2 = build_example_ast();
    
    // 存储到NodeStore
    NodeHash hash1 = store.store(ast1);
    NodeHash hash2 = store.store(ast2);
    
    std::cout << "AST 1 hash: " << hash1 << std::endl;
    std::cout << "AST 2 hash: " << hash2 << std::endl;
    
    // 相同内容的AST应该有相同的哈希
    if (hash1 == hash2) {
        std::cout << "✓ Content addressing works: identical ASTs have same hash" << std::endl;
    } else {
        std::cout << "✗ Content addressing failed: identical ASTs have different hashes" << std::endl;
    }
    
    // 创建引用节点
    auto ref_node = Node::make_reference(hash1);
    std::cout << "Created reference node with hash: " << ref_node->as_reference() << std::endl;
}

// 示例：展示CoW特性
void demonstrate_cow() {
    std::cout << "\n=== Copy-on-Write Demo ===" << std::endl;
    
    auto original = build_example_ast();
    auto shared_copy = original;  // 共享同一个对象
    
    std::cout << "Original and copy share same object: " << (original == shared_copy) << std::endl;
    
    // 修改会触发CoW（虽然当前实现还没有完全实现CoW）
    (*shared_copy)["modified"] = Node::make_bool(true);
    
    std::cout << "After modification, objects are still shared: " << (original == shared_copy) << std::endl;
    std::cout << "Note: Full CoW implementation would create separate objects here" << std::endl;
}

// 示例：序列化和可视化AST
void demonstrate_serialization() {
    std::cout << "\n=== AST Serialization Demo ===" << std::endl;
    
    auto ast = build_example_ast();
    
    std::cout << "AST structure for: add(2 + 3, 4)" << std::endl;
    std::cout << ast->to_string() << std::endl;
    
    // 计算哈希
    std::cout << "\nAST hash: " << ast->hash() << std::endl;
}

// 示例：构建更复杂的AST
NodePtr build_complex_ast() {
    // 表示代码：
    // function fibonacci(n) {
    //     if (n <= 1) return n;
    //     return fibonacci(n-1) + fibonacci(n-2);
    // }
    
    auto param_n = create_identifier("n");
    auto literal_1 = create_literal(1);
    auto condition = create_binary_op("<=", param_n, literal_1);
    
    // if语句的then分支：return n
    auto return_n = create_ast_node("return");
    (*(*return_n)["children"]).push_back(create_identifier("n"));
    
    // 递归调用：fibonacci(n-1)
    auto n_minus_1 = create_binary_op("-", create_identifier("n"), create_literal(1));
    auto fib_n_minus_1 = create_call("fibonacci", {n_minus_1});
    
    // 递归调用：fibonacci(n-2)
    auto n_minus_2 = create_binary_op("-", create_identifier("n"), create_literal(2));
    auto fib_n_minus_2 = create_call("fibonacci", {n_minus_2});
    
    // return fibonacci(n-1) + fibonacci(n-2)
    auto return_expr = create_binary_op("+", fib_n_minus_1, fib_n_minus_2);
    auto return_stmt = create_ast_node("return");
    (*(*return_stmt)["children"]).push_back(return_expr);
    
    // if语句
    auto if_stmt = create_ast_node("if");
    (*(*if_stmt)["children"]).push_back(condition);
    (*(*if_stmt)["children"]).push_back(return_n);
    (*(*if_stmt)["children"]).push_back(return_stmt);
    
    // 函数体
    auto body = create_ast_node(ast_types::BLOCK);
    (*(*body)["children"]).push_back(if_stmt);
    
    // 函数定义
    auto function = create_function("fibonacci", body);
    (*(*function)["parameters"]).push_back(create_identifier("n"));
    
    return function;
}

void demonstrate_complex_ast() {
    std::cout << "\n=== Complex AST Demo ===" << std::endl;
    
    auto complex_ast = build_complex_ast();
    
    std::cout << "Complex AST structure for fibonacci function:" << std::endl;
    std::cout << complex_ast->to_string() << std::endl;
    
    std::cout << "\nComplex AST hash: " << complex_ast->hash() << std::endl;
    
    // 存储到NodeStore
    auto& store = NodeStore::instance();
    NodeHash hash = store.store(complex_ast);
    std::cout << "Stored in NodeStore with hash: " << hash << std::endl;
}

int main() {
    std::cout << "LDDK AST Node Structure Demo" << std::endl;
    std::cout << "============================" << std::endl;
    
    try {
        demonstrate_serialization();
        demonstrate_content_addressing();
        demonstrate_cow();
        demonstrate_complex_ast();
        
        std::cout << "\n============================" << std::endl;
        std::cout << "All demos completed successfully! ✓" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cout << "\nDemo failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "\nDemo failed with unknown exception" << std::endl;
        return 1;
    }
}
