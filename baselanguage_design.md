# Baselanguage设计文档（v0.1）
## 核心设计原则
- 目标为语言工作台（Langage Workspace）：直接连接架构团队与开发团队，并对个人开发者友好。
- 文本工作流优先：抛弃AST编辑器，专注于text-based的开发，支持标准工具链（如版本控制、编辑器插件）。
- LLVM集成
- 自举能力
- 正交特性
- 目标群体需求：支持低层操作（如指针、内存管理）、并发、微服务架构和嵌入式开发。
- 鼓励表达原始设计：以最好的可读性表达用户的预期及整体的设计，并保留原始意图到代码的完整设计过程。
- 渐进式抽象：鼓励用户在需要时扩展语言，而不是一开始就设计所有特性。对于原型代码
- 简化语言设计：语言特性应尽可能简单，避免复杂性和歧义。优先关注语言的自举能力。
- 语言即操作系统

## 语言特性框架
### 1. 语法基础
- C-like语法：采用类似C的语法结构（如大括号、分号）。
- 表达式基语言：几乎所有结构都是表达式，允许更灵活的代码组合（如if表达式、块表达式）。

### 2. 类型系统
- 同像性：允许程序在编译时操作和生成代码，并保持类型系统有一个统一且开放的设计，可以由用户添加功能
- 首先实现必要特性，其余特性由库引入。
- 自举所需的必要特性：
    - 原始类型：整数、浮点数、布尔值、字符、字符串
    - 指针类型：支持指针算术和直接内存访问
    - 数组类型：支持数组访问和切片
    - 结构体类型：支持结构体访问和解构
    - 枚举类型：支持枚举访问和解构
    - 静态类型系统：编译时类型检查，确保安全性和性能。
- 其余核心特性：
    - 效应系统：副作用按类型管理
    - 支持泛型，联合类型与代数数据类型。
    - 良好的扩展能力，用户可能需要支持依赖类型，势必要求类型系统是图灵完备的
- 由库提供的特性：
    - 类型推断：支持局部类型推断，减少样板代码。
    - 空安全：通过可选类型表示可能为null的变量
    - 鸭子类型：允许通过约束的方式，只关心一个值是否拥有所需的方法
    - 交叉类型：可以将多个类型合并为一个
    - 依赖类型：允许类型依赖于值
    - 线性类型：确保一个值有且仅被使用一次
    - 类型别名和新类型：允许创建类型别名和强类型定义，增强代码可读性和安全性。

### 3. 内存管理
- 手动内存管理：提供显式的内存分配和释放操作（如`malloc`/`free`类似函数），适合系统编程。
- 可选所有权系统：借鉴Rust的所有权模型，但作为库提供。
- 无垃圾收集：默认无GC，保持低开销，但允许通过库集成GC。

### 4. 函数和抽象
- 使用`->`表示纯函数类型，如`fn add(a: int, b: int) -> int`
- 使用`->`和`{}`表示有副作用的函数类型，如`fn func(a: int, b: int) -> {IO} int`
- 匿名函数：使用`=>`表示，如`a => a + 1`或`(a, b) => a + b`
- 一等公民函数：函数可以作为变量传递、返回和存储，支持高阶函数。
- 闭包：支持闭包，如`(a, b) [x] => a + b + x`
- 多返回值：函数可以返回多个值，通过元组或结构体实现。
- 函数重载：基于参数类型的重载，但保持简单，避免歧义。

### 5. 控制流
- 传统控制结构：包括`if`、`else`、`while`、`for`循环（基于迭代器或范围）。
- `match`表达式：模式匹配支持，用于枚举和类型解构，增强代码表达力。
- 错误处理：使用Result类型和Option类型进行显式错误处理，避免异常带来的开销（与系统编程兼容）。
- 异常处理：作为可选特性，由库提供
- 异步编程：支持async/await语法，用于微服务和IO密集型应用，与效应系统紧密结合。
- 线程原语：内置线程和同步原语，如`thread`、`mutex`、`condvar`等。
- 原子操作和内存屏障：内置原子类型和操作，用于内核和嵌入式开发。

### 6. 模块系统
- 命名空间：支持命名空间，用于模块化开发。
- 显式声明和导入：明确模块的边界和依赖关系
- 基于文件/目录的物理映射
- 依赖循环检测与规避
- 版本依赖与冲突解决
- 条件编译与平台特定模块
- 简单的入门门槛
- 可组合的单元：模块本身是“一等公民”。这意味着模块可以像值一样被传递、返回、甚至由函数动态生成。
- 参数化模块：块可以接受其他模块作为参数，并返回一个新的模块。这本质上是一种在模块级别上的函数。
- 能力安全与依赖注入： 权限管理
- 基于内容寻址：模块/包不是通过名字和版本，而是通过其内容的哈希值来唯一标识。
- 类型导向的导入：译器可以根据需要的类型，自动为你寻找并导入实现了相应接口/类型类的模块。
- 统一的可视化与工具链：为 IDE、语言服务器（LSP）、文档生成器 等工具提供丰富的元信息
- 可验证的构建与沙盒化：构建过程是完全可重现和可验证的，通过声明所有依赖的精确哈希值。并且构建过程在沙盒中进行，无法访问未声明的网络或文件系统资源。

### 7. 元编程和扩展性
- 多语法支持：允许用户定义自己的语法扩展，以适应特定领域的DSL需求。以横线隔离不同的语法
- 即时DSL定义：对于使用量少的DSL,允许将DSL定义直接放在使用处
- 宏系统：支持卫生宏（hygienic macros）或语法宏（syntax macros），允许编译时代码生成和DSL定义。宏应基于抽象语法树（AST）操作，但通过文本界面暴露。
- 编译时计算：允许在编译时执行函数且明确区分编译阶段，使用代码层级来区分。
代码块默认是无层级的，词法解析也不会为代码块设置层级，从语法分析开始，编译器不断处理高层级的代码或无层级的代码，输出低层级的代码，若代码层级到达0,表明代码被编译器接受，不再抛出错误。
负数层级用于优化和代码生成。

### 8. 工具链集成
- LLVM后端：编译器直接生成LLVM IR，利用LLVM的优化和代码生成。
- 调试支持：生成标准调试信息（如DWARF），与GDB/LLDB兼容。
- 交叉编译：支持轻松交叉编译到多种架构（如ARM、x86、RISC-V）。
- IDE支持：提供语言服务器协议（LSP）实现，用于编辑器集成（如VS Code、Vim）。

### 9. 标准库
TODO

## 自举策略
- 阶段1：先用一个简单的高级语言（如C++）实现编译器的前端（词法分析、语法分析、类型检查），输出LLVM IR。
- 阶段2：用这个编译器编译自身的最小子集（核心语言），逐步替换外部依赖。
- 阶段3：完全用自身语言重写编译器，实现自举。核心语言应足够小，以便快速实现自举。

## 语法设计细节


### 1. 词法规范
- **关键字**：`var`、`const`、`fn`、`if`、`else`、`while`、`for`、`match`、`true`、`false`、`namespace`、`type`、`block`
- **标识符**：
  - 标准标识符：`[a-zA-Z_][a-zA-Z0-9_]*`
  - 原始标识符：反引号包围的任意字符（如 `` `class` `` 或 `` `some-name` ``）
- **注释**：
  - 单行注释：`//` 后跟任意字符（直到行尾）
  - 多行注释：`/*` 和 `*/` 之间（**不允许嵌套**）
- **字面量**：
  - 数字：`[0-9]`后接任意个字母或数字（可包含 `_`、`.`、`+`、`-`，如 `1_000.5e-3`）
  - 字符串：双引号包围（支持转义，**不允许未转义换行**）
  - 字符：单引号包围（支持转义，**不允许未转义换行**）
- **操作符规则**：
  - 二元操作符：**必须**前后有空格（如 `a + b`），对于无歧义且不加空格的情况，编译器给出警告
  - 一元操作符：**禁止**后跟空格（如 `-a`）
  - 后缀操作符：**禁止**前加空格（如 `a()`、`a[b]`、`a.b`）
  - 操作符组成：Unicode类别（Sm/Pd/Po/Sc），排除 `+ - ! ~ * / %`

### 2. 表达式系统
- **基础表达式**：
  ```ebnf
  <expression> ::= <common_binary_expr>  // 强制空格分隔的二元操作
  <common_binary_expr> ::= <additive_expr> [ <whitespace> ("&&" | "||" | ...) <whitespace> <additive_expr> ]
  ```
- **层次化操作符优先级**：
  1. 前缀操作：`+a`、`-a`、`!a`、`~a`（**无空格**）
  2. 乘法类：`a * b`、`a / b`、`a % b`（空格分隔）
  3. 加法类：`a + b`、`a - b`（空格分隔）
  4. 其他二元操作：`a == b`、`a < b` 等（空格分隔）
- **复合表达式**：
  - 函数调用：`func(arg1, arg2)`（**禁止空格**：`func (arg)` 非法）
  - 成员访问：`obj.field` 或 ``obj.`field-name` ``
  - 索引访问：`arr[index]`（等价列表语法，视为后缀操作符）
  - 列表：`[1, 2, 3]`（支持尾随逗号）
  - 元组：`{1, "text"}`（使用花括号，支持尾随逗号）
- **特殊表达式**：
  - 块表达式：`{ stmt; expr }`（最后表达式为返回值）
  - 函数类型：`fn(a: int) -> {Effect} bool`

### 3. 语句规范
- **声明语句**：
  ```ebnf
  // 变量（带可选类型/初始化/块）
  var ident [: type] [ = expr | <block> ]
  
  // 常量（必须初始化）
  const ident [: type] = expr
  
  // 类型别名
  type ident [: base] = type
  
  // 函数（签名+表达式/块）
  fn ident <function_sig> [ = expr | <block> ]
  
  // 命名空间（多级）
  namespace a.b.c { ... }
  ```
- **控制流语句**：
  ```ebnf
  if (<expr>) <block>         // 条件必须括号
  while (<expr>) <block>      // 条件必须括号
  for (<init_expr>; <cond>; <step>) <block>  // 类C语法
  match (<expr>) { ... }      // 模式匹配
  ```
- **增强块**（多行文本处理）：
  ```ebnf
  block NAME [: TYPE] !       // 类型可选，注意空格且感叹号后必须为换行，无空白
  ---                         // 分隔符（3~120个 '-' 或 '='）
  Indented content            // 缩进必须一致，且与分隔符的缩进一致
  ---                         // 相同分隔符
  ```

### 4. 类型系统
- **类型语法**：
  ```ebnf
  <type> ::= <base_type> ( "[" [<type> ("," <type>)*] "]" )*  // 支持多维
  <base_type> ::= ident | <function_sig>
  ```
- **函数签名**：
  ```ebnf
  fn (param: type, ...) -> {Effect} ReturnType  // 效果标签可选
  ```

### 5. 关键变更说明
1. **标识符扩展**：
   - 新增原始标识符 `` `name` `` 支持特殊符号
   - 命名空间路径 `a.b.c` 支持多级

2. **严格空格规则**：
   - 二元操作符**必须**空格包围：`a+b` → `a + b`
   - 函数调用**禁止**空格：`func (arg)` → `func(arg)`

3. **块表达式强化**：
   - 显式返回机制：`{ ...; expr }`（最后表达式为值）
   - 可选类型注解：`: type { ... }`

5. **表达式语法调整**：
   - 元组使用 `{1, 2}` 而非 `(1, 2)`
   - 索引访问统一为 `a[i]` 语法
   - 函数类型效果标签移至返回类型前

### EBNF定义

```ebnf
(* 基础元素 *)
<program>        ::= (<statement> | <comment>)*
<statement>      ::= <declaration> ";" | <expression> ";" | <control_flow> | <enhanced_block>
<block>          ::= [ ":" <type>] "{" (<statement> | <comment>)* <expression>? "}"

(* 声明语句 *)
<declaration>    ::= 
      "var" <identifier> (( [ ":" <type> ] [ <whitespace> "=" <whitespace> <expression> ] ) | <block> )?
    | "const" <identifier> (( [ ":" <type> ] <whitespace> "=" <whitespace> <expression> ))
    | "namespace" <namespace_path> <block>
    | "type" <identifier> ( [ ":" <type> ] (<whitespace> "=" <whitespace> <type>) | <block> )
    | "fn" <identifier> <function_sig> ( ( <whitespace> "=" <whitespace> <expression> ) | <block> )
<namespace_path> ::= <identifier> ("." <identifier>)*  (* 多级命名空间支持 *)

(* 控制结构 *)
<control_flow>   ::= <control_kw> [<paren_expr>] <block>
<control_kw>      ::= "if" | "while" | "for" | "match" | "else"
<paren_expr>      ::= "(" <expression> ")"

(* 表达式系统 *)
<expression>     ::= <common_binary_expr>
<common_binary_expr> ::= <additive_expr> [ <whitespace> <other_operator> <whitespace> <additive_expr> ] (* 强制要求二元操作符包含前后空格，且强制要求括号来区分优先级 *)
<additive_expr>  ::= <multiplicative_expr> [ <whitespace> ("+" | "-") <whitespace> <additive_expr> ]
<multiplicative_expr> ::= (<prefix_expr> | <suffix_expr>) [ <whitespace> ("*" | "/" | "%") <whitespace> <multiplicative_expr> ]
<prefix_expr>    ::= ( <prefix_operator> )* <primary_expr> (* 强制要求操作符后不包含空格 *)
<suffix_expr>      ::= <primary_expr> ( <member_access> | <function_call> | <index_access> | <strict_operator> )* (* 强制要求操作符前不包含空格 *)
<primary_expr>   ::= <identifier>
                   | <literal> 
                   | <paren_expr>
                   | <block>
                   | <list_expr>
                   | <tuple_expr>
<list_expr>      ::= "[" [<expression> ("," <expression>)* [","]] "]"
<tuple_expr>     ::= "{" [<expression> ("," <expression>)* [","]] "}"  (* 支持尾随逗号 *)
<member_access>    ::= "." ( <identifier> | <raw_identifier> )
<function_call>    ::= "(" [ <expression> ( "," <expression> )* [","]] ")"
<index_access>     ::= <list_expr>

(* 类型系统 *)
<type>           ::= <base_type> [ "[" [<type> ("," <type>)* [","]] "]" ]*
<base_type>      ::= <identifier> | <function_sig>
<function_sig>   ::= "(" [<param> ("," <param>)*] ")" "->" [<effect>] <type>
<param>          ::= <identifier> ":" <type>
<effect>         ::= "{" <expression> "}"

(* 增强块结构 *)
<enhanced_block> ::= "block" <identifier> [
                        ([":" <type>] "!\n" (* 以感叹号结尾 *)
                            <indent_string> <delimiter> "\n" (* 起始处的delimiter *)
                            <indent_string> <block_content> "\n" (* 整个块内的<indent_string>必须相同 *)
                            <indent_string> <delimiter> (* 与起始处的delimiter相同 *)
                        ) | <block>]
<delimiter>      ::= ("-" | "="){3,120}  (* 至少3个重复，最多120个重复 *)
<indent_string>  ::= <strict_whitespace>
<block_content>   ::= (.* \n)*  (* 任意多行内容，不包含delimiter *)

(* 词法元素 *)
<identifier>     ::= <name_start> <name_char>* | <raw_identifier>
<raw_identifier> ::= "`" <raw_identifier_content> "`"
<operator>       ::= <other_operator> [<name_char> | <other_operator>]* (* 允许字母扩展 *)
<strict_operator> ::= <other_operator>+
<prefix_operator> ::= "+" | "-" | "!" | "~"
<literal>        ::= <number> | <string> | <char>
<number>         ::= [0-9]+ (<name_char> | "." | "_" | "+" | "-")* (* 允许下划线，允许扩展语法 *)
<string>         ::= "\"" <string_content> "\""
<char>           ::= "'" <char_content> "'"

(* 词法细节 *)
<name_start>     ::= [a-zA-Z_]
<name_char>      ::= [a-zA-Z0-9_]
<string_content> ::= ? 允许转义字符，不允许换行和双引号 ?
<char_content>   ::= ? 允许转义字符，不允许换行和单引号 ?
<raw_identifier_content> ::= ? 除"`"外的任意字符, 不允许转义及换行 ?
<other_operator> ::= ? Unicode Sm、Pd、Po、Sc Category string, 必须使用 NFC 编码形式, 且不包含+、-、!、~、*、/、% ?

(* 注释系统 *)
<comment>        ::= <line_comment> | <block_comment>
<line_comment>   ::= "//" .* \n
<block_comment>  ::= "/*" (.*? | \n)* "*/" (* 不允许嵌套 *)

(* 空格处理 *)
<whitespace>     ::= [ \t\n\r]+
<strict_whitespace> ::= " "+

```

### 词法分析器API

参考：[lexer.h](src/llvm-frontend/lexer.h)

```cpp
struct Token : public std::enable_shared_from_this<Token> {
    TokenType type;
    std::u32string value;
    std::u32string nest_state;
    std::shared_ptr<Token> prev;
};
using StateVariant = std::variant<
    ErrorState, // 0
    NormalState, // 1
    LineCommentState,
    BlockCommentState,
    StringState,
    CharState,
    RawIdentifierState,
    OperatorState,
    IdentifierState,
    NumberState,
    EnhancedBlockState    
>;
struct LexerStateData {
    StateVariant state = NormalState{};
    std::u32string accumulated_value = U"";
    NestingState nesting_state = NestingState{};  // 括号嵌套状态

    LexerStateData new_state(StateVariant state);
    LexerStateData get_modified_state(StateVariant new_state);
    std::u32string dump_state()const;
};
class LexerState {
public:
    struct ExtendedState : public std::enable_shared_from_this<ExtendedState>{
        LexerStateData data = {NormalState{false}};
        std::shared_ptr<Token> token;
        std::shared_ptr<Token> last_token;
        std::shared_ptr<ExtendedState> prev;
    };
    LexerState():data({std::make_shared<ExtendedState>()}){}

    // 实用函数
    LexerState eat_char(char32_t c, SoueceInfo *info)const;
    std::u32string dump_state()const;
    LexerState finalize(SoueceInfo *info)const;
    std::list<std::shared_ptr<Token>> get_tokens()const;

    // 词法分析核心函数
    static std::pair<std::shared_ptr<Token>, std::vector<LexerStateData>> eat_char(LexerStateData state,char32_t c);
private:
    std::list<std::shared_ptr<ExtendedState>> data;
    std::list<std::shared_ptr<Token>> tokens;
};

```

### 核心结构设计

1. 以类似BSON小文档的形式组织，称之为节点
2. 能对文档实现稳定的哈希算法用于内容寻址
3. 支持CoW
    * 节点可能链接到其它节点上，组成DAG
    * 需要支持引用计数
    * 需要探讨如何实现节点引用的序列化，似乎应该直接用哈希值
4. 支持序列化和反序列化
5. 提供类似nlohmann/json.hpp的接口用于方便的构建和访问
6. 单头文件形式，文件名未定
7. 用于AST节点与类型系统

