# 编译说明

Windows cmd:
```
"D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --build d:/git/LDDK/build --config Debug --target lexer_test -j 16 --
```

Windows power shell:
```
."D:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --build d:/git/LDDK/build --config Debug --target lexer_test -j 16 --
```

Linux:
```bash
./build.sh
```

# 运行分词器测试

Linux:
```bash
# 注意工作目录
./build/lexer_test tests/lexer/lexer.lddk tests/lexer/test_brackets.lddk
```

或

```
./build.sh && ./build/lexer_test
```