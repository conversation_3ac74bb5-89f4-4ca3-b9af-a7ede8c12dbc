## Lexer问题修复

### 测试程序的结构化错误处理

1. 优化错误显示，目前预期的错误状态打印与没有通过的测试混杂在一起无法分辨，且不同测试的输出难以区分
2. 目前的Token输出比对没有对括号嵌套增加支持，希望能检测`nest_state`并判断是否相等

### 语法支持完善

1. （部分完成）希望词法分析阶段能处理括号嵌套，并检测括号嵌套错误
   * 无法通过测试用例，右括号似乎会吞掉前面的字符

## Parser整体设计

### 基础数据类型设计

由于要支持基于内容寻址，以及可复现构建，基础数据类型需要能被hash到一个稳定的索引上，考虑在AST的数据结构上就实现这一点，以同像性为重，未来类型系统设计考虑复用这个数据格式。从这个需求来看CoW似乎是有必要实现的。

目前调研了一些JSON/BSON库，都不好做稳定hash，并且CoW支持不好。

整理了一些要点：
1. 以类似BSON小文档的形式组织，称之为节点
2. 能对文档实现稳定的哈希算法用于内容寻址
3. 支持CoW
    * 节点可能链接到其它节点上，组成DAG
    * 需要支持引用计数
    * 需要探讨如何实现节点引用的序列化，似乎应该直接用哈希值
4. 支持序列化和反序列化
5. 提供类似nlohmann/json.hpp的接口用于方便的构建和访问
6. 单头文件形式，文件名未定

### Parser状态设计

待定，考虑参考分词器的设计

## 整体要求

保持简洁，尽量单文件内完成所需的逻辑。
<!-- 测试用例编译方法：运行windows_build.bat
运行测试方法：`."D:/git/LDDK/build/Debug/lexer_test.exe"` -->
测试用例编译方法：`./build.sh`
运行测试方法：`./build/lexer_test`（可以传入测试文件的相对路径，相对于工作目录）
编译并测试：`./build.sh && ./build/lexer_test`