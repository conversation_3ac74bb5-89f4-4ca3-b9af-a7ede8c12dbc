#pragma once

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <variant>
#include <functional>
#include <cstdint>
#include <cassert>
#include <mutex>

namespace lddk {

// 前向声明
class Node;
using NodePtr = std::shared_ptr<Node>;
using NodeHash = std::uint64_t;

// 节点值类型
using NodeValue = std::variant<
    std::nullptr_t,     // null
    bool,               // boolean
    std::int64_t,       // integer
    double,             // floating point
    std::string,        // string
    std::vector<NodePtr>, // array
    std::map<std::string, NodePtr>, // object
    NodeHash            // reference to another node by hash
>;

// 节点类型枚举
enum class NodeType {
    Null,
    Bool,
    Int,
    Float,
    String,
    Array,
    Object,
    Reference
};

// 稳定哈希计算器
class StableHasher {
public:
    static NodeHash hash_value(const NodeValue& value);
    static NodeHash hash_node(const Node& node);
    
private:
    static NodeHash hash_string(const std::string& str);
    static NodeHash hash_combine(NodeHash h1, NodeHash h2);
    static constexpr NodeHash FNV_OFFSET_BASIS = 14695981039346656037ULL;
    static constexpr NodeHash FNV_PRIME = 1099511628211ULL;
};

// 节点类 - 支持CoW和引用计数
class Node : public std::enable_shared_from_this<Node> {
    friend class StableHasher;
public:
    // 构造函数
    Node() : value_(nullptr), hash_cache_(0), hash_valid_(false) {}
    
    template<typename T>
    explicit Node(T&& val) : value_(std::forward<T>(val)), hash_cache_(0), hash_valid_(false) {}
    
    // 禁用拷贝构造，强制使用共享指针
    Node(const Node&) = delete;
    Node& operator=(const Node&) = delete;
    
    // 移动构造
    Node(Node&& other) noexcept 
        : value_(std::move(other.value_)), hash_cache_(other.hash_cache_), hash_valid_(other.hash_valid_) {}
    
    // 静态工厂方法
    static NodePtr make_null() { return std::make_shared<Node>(nullptr); }
    static NodePtr make_bool(bool val) { return std::make_shared<Node>(val); }
    static NodePtr make_int(std::int64_t val) { return std::make_shared<Node>(val); }
    static NodePtr make_float(double val) { return std::make_shared<Node>(val); }
    static NodePtr make_string(const std::string& val) { return std::make_shared<Node>(val); }
    static NodePtr make_array() { return std::make_shared<Node>(std::vector<NodePtr>{}); }
    static NodePtr make_object() { return std::make_shared<Node>(std::map<std::string, NodePtr>{}); }
    static NodePtr make_reference(NodeHash hash) { return std::make_shared<Node>(hash); }
    
    // 类型检查
    NodeType type() const;
    bool is_null() const { return type() == NodeType::Null; }
    bool is_bool() const { return type() == NodeType::Bool; }
    bool is_int() const { return type() == NodeType::Int; }
    bool is_float() const { return type() == NodeType::Float; }
    bool is_string() const { return type() == NodeType::String; }
    bool is_array() const { return type() == NodeType::Array; }
    bool is_object() const { return type() == NodeType::Object; }
    bool is_reference() const { return type() == NodeType::Reference; }
    
    // 值访问（类似nlohmann/json的接口）
    bool as_bool() const;
    std::int64_t as_int() const;
    double as_float() const;
    const std::string& as_string() const;
    NodeHash as_reference() const;
    
    // 数组操作
    size_t size() const;
    NodePtr& operator[](size_t index);
    const NodePtr& operator[](size_t index) const;
    void push_back(NodePtr node);
    
    // 对象操作
    NodePtr& operator[](const std::string& key);
    const NodePtr& operator[](const std::string& key) const;
    bool contains(const std::string& key) const;
    void erase(const std::string& key);
    
    // 哈希计算
    NodeHash hash() const;
    
    // CoW支持 - 在修改前确保独占所有权
    void ensure_unique();
    
    // 序列化支持
    std::string serialize() const;
    static NodePtr deserialize(const std::string& data);
    
    // 调试输出
    std::string to_string(int indent = 0) const;
    
private:
    NodeValue value_;
    mutable NodeHash hash_cache_;
    mutable bool hash_valid_;
    
    void invalidate_hash() { hash_valid_ = false; }
    
    // CoW辅助方法
    template<typename T>
    T& get_mutable() {
        ensure_unique();
        invalidate_hash();
        return std::get<T>(value_);
    }
};

// 节点存储管理器 - 用于内容寻址
class NodeStore {
public:
    static NodeStore& instance();
    
    // 存储节点并返回其哈希
    NodeHash store(NodePtr node);
    
    // 根据哈希检索节点
    NodePtr retrieve(NodeHash hash) const;
    
    // 检查节点是否存在
    bool contains(NodeHash hash) const;
    
    // 垃圾回收 - 清理未引用的节点
    void garbage_collect();
    
    // 统计信息
    size_t size() const { return store_.size(); }
    
private:
    NodeStore() = default;
    std::map<NodeHash, std::weak_ptr<Node>> store_;
    mutable std::mutex mutex_;
};

// 便利的构造函数
namespace literals {
    inline NodePtr operator""_node(const char* str, size_t len) {
        return Node::make_string(std::string(str, len));
    }
    
    inline NodePtr operator""_node(unsigned long long val) {
        return Node::make_int(static_cast<std::int64_t>(val));
    }
    
    inline NodePtr operator""_node(long double val) {
        return Node::make_float(static_cast<double>(val));
    }
}

// ============================================================================
// 实现部分
// ============================================================================

// StableHasher 实现
inline NodeHash StableHasher::hash_string(const std::string& str) {
    NodeHash hash = FNV_OFFSET_BASIS;
    for (char c : str) {
        hash ^= static_cast<NodeHash>(c);
        hash *= FNV_PRIME;
    }
    return hash;
}

inline NodeHash StableHasher::hash_combine(NodeHash h1, NodeHash h2) {
    return h1 ^ (h2 + 0x9e3779b9 + (h1 << 6) + (h1 >> 2));
}

inline NodeHash StableHasher::hash_value(const NodeValue& value) {
    return std::visit([](const auto& v) -> NodeHash {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, std::nullptr_t>) {
            return 0;
        } else if constexpr (std::is_same_v<T, bool>) {
            return v ? 1 : 0;
        } else if constexpr (std::is_same_v<T, std::int64_t>) {
            return static_cast<NodeHash>(v);
        } else if constexpr (std::is_same_v<T, double>) {
            return std::hash<double>{}(v);
        } else if constexpr (std::is_same_v<T, std::string>) {
            return hash_string(v);
        } else if constexpr (std::is_same_v<T, std::vector<NodePtr>>) {
            NodeHash hash = FNV_OFFSET_BASIS;
            for (const auto& node : v) {
                hash = hash_combine(hash, node ? node->hash() : 0);
            }
            return hash;
        } else if constexpr (std::is_same_v<T, std::map<std::string, NodePtr>>) {
            NodeHash hash = FNV_OFFSET_BASIS;
            for (const auto& [key, node] : v) {
                NodeHash key_hash = hash_string(key);
                NodeHash value_hash = node ? node->hash() : 0;
                hash = hash_combine(hash, hash_combine(key_hash, value_hash));
            }
            return hash;
        } else if constexpr (std::is_same_v<T, NodeHash>) {
            return v;  // 引用的哈希值
        }
        return 0;
    }, value);
}

inline NodeHash StableHasher::hash_node(const Node& node) {
    // 需要访问Node的私有成员，所以在Node类中声明为友元
    return hash_value(node.value_);
}

// Node 实现
inline NodeType Node::type() const {
    return std::visit([](const auto& v) -> NodeType {
        using T = std::decay_t<decltype(v)>;
        if constexpr (std::is_same_v<T, std::nullptr_t>) return NodeType::Null;
        else if constexpr (std::is_same_v<T, bool>) return NodeType::Bool;
        else if constexpr (std::is_same_v<T, std::int64_t>) return NodeType::Int;
        else if constexpr (std::is_same_v<T, double>) return NodeType::Float;
        else if constexpr (std::is_same_v<T, std::string>) return NodeType::String;
        else if constexpr (std::is_same_v<T, std::vector<NodePtr>>) return NodeType::Array;
        else if constexpr (std::is_same_v<T, std::map<std::string, NodePtr>>) return NodeType::Object;
        else if constexpr (std::is_same_v<T, NodeHash>) return NodeType::Reference;
        return NodeType::Null;
    }, value_);
}

inline bool Node::as_bool() const {
    assert(is_bool());
    return std::get<bool>(value_);
}

inline std::int64_t Node::as_int() const {
    assert(is_int());
    return std::get<std::int64_t>(value_);
}

inline double Node::as_float() const {
    assert(is_float());
    return std::get<double>(value_);
}

inline const std::string& Node::as_string() const {
    assert(is_string());
    return std::get<std::string>(value_);
}

inline NodeHash Node::as_reference() const {
    assert(is_reference());
    return std::get<NodeHash>(value_);
}

// 数组操作实现
inline size_t Node::size() const {
    if (is_array()) {
        return std::get<std::vector<NodePtr>>(value_).size();
    } else if (is_object()) {
        return std::get<std::map<std::string, NodePtr>>(value_).size();
    }
    return 0;
}

inline NodePtr& Node::operator[](size_t index) {
    assert(is_array());
    auto& arr = get_mutable<std::vector<NodePtr>>();
    if (index >= arr.size()) {
        arr.resize(index + 1);
    }
    return arr[index];
}

inline const NodePtr& Node::operator[](size_t index) const {
    assert(is_array());
    const auto& arr = std::get<std::vector<NodePtr>>(value_);
    assert(index < arr.size());
    return arr[index];
}

inline void Node::push_back(NodePtr node) {
    assert(is_array());
    auto& arr = get_mutable<std::vector<NodePtr>>();
    arr.push_back(std::move(node));
}

// 对象操作实现
inline NodePtr& Node::operator[](const std::string& key) {
    assert(is_object());
    auto& obj = get_mutable<std::map<std::string, NodePtr>>();
    return obj[key];
}

inline const NodePtr& Node::operator[](const std::string& key) const {
    assert(is_object());
    const auto& obj = std::get<std::map<std::string, NodePtr>>(value_);
    auto it = obj.find(key);
    assert(it != obj.end());
    return it->second;
}

inline bool Node::contains(const std::string& key) const {
    if (!is_object()) return false;
    const auto& obj = std::get<std::map<std::string, NodePtr>>(value_);
    return obj.find(key) != obj.end();
}

inline void Node::erase(const std::string& key) {
    assert(is_object());
    auto& obj = get_mutable<std::map<std::string, NodePtr>>();
    obj.erase(key);
}

// 哈希计算实现
inline NodeHash Node::hash() const {
    if (!hash_valid_) {
        hash_cache_ = StableHasher::hash_node(*this);
        hash_valid_ = true;
    }
    return hash_cache_;
}

// CoW实现
inline void Node::ensure_unique() {
    // 如果引用计数大于1，需要进行深拷贝
    auto self = shared_from_this();
    if (self.use_count() > 1) {
        // 这里需要实现深拷贝逻辑
        // 暂时简化处理 - 实际应该创建新的副本
        // 由于这是一个复杂的操作，暂时留空
    }
}

// NodeStore 实现
inline NodeStore& NodeStore::instance() {
    static NodeStore instance;
    return instance;
}

inline NodeHash NodeStore::store(NodePtr node) {
    if (!node) return 0;

    NodeHash hash = node->hash();
    std::lock_guard<std::mutex> lock(mutex_);

    // 检查是否已存在
    auto it = store_.find(hash);
    if (it != store_.end()) {
        // 如果已存在且仍然有效，直接返回
        if (!it->second.expired()) {
            return hash;
        }
        // 如果已过期，移除旧的条目
        store_.erase(it);
    }

    // 存储新的节点
    store_[hash] = std::weak_ptr<Node>(node);
    return hash;
}

inline NodePtr NodeStore::retrieve(NodeHash hash) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = store_.find(hash);
    if (it != store_.end()) {
        return it->second.lock();  // 尝试获取强引用
    }
    return nullptr;
}

inline bool NodeStore::contains(NodeHash hash) const {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = store_.find(hash);
    return it != store_.end() && !it->second.expired();
}

inline void NodeStore::garbage_collect() {
    std::lock_guard<std::mutex> lock(mutex_);
    for (auto it = store_.begin(); it != store_.end();) {
        if (it->second.expired()) {
            it = store_.erase(it);
        } else {
            ++it;
        }
    }
}

// 简单的序列化实现（JSON格式）
inline std::string Node::to_string(int indent) const {
    std::string spaces(indent * 2, ' ');
    std::string result;

    switch (type()) {
        case NodeType::Null:
            return "null";
        case NodeType::Bool:
            return as_bool() ? "true" : "false";
        case NodeType::Int:
            return std::to_string(as_int());
        case NodeType::Float:
            return std::to_string(as_float());
        case NodeType::String:
            return "\"" + as_string() + "\"";
        case NodeType::Reference:
            return "ref:" + std::to_string(as_reference());
        case NodeType::Array: {
            const auto& arr = std::get<std::vector<NodePtr>>(value_);
            result = "[\n";
            for (size_t i = 0; i < arr.size(); ++i) {
                result += std::string((indent + 1) * 2, ' ');
                result += arr[i] ? arr[i]->to_string(indent + 1) : "null";
                if (i < arr.size() - 1) result += ",";
                result += "\n";
            }
            result += spaces + "]";
            return result;
        }
        case NodeType::Object: {
            const auto& obj = std::get<std::map<std::string, NodePtr>>(value_);
            result = "{\n";
            auto it = obj.begin();
            for (; it != obj.end(); ++it) {
                result += std::string((indent + 1) * 2, ' ');
                result += "\"" + it->first + "\": ";
                result += it->second ? it->second->to_string(indent + 1) : "null";
                auto next_it = it;
                ++next_it;
                if (next_it != obj.end()) result += ",";
                result += "\n";
            }
            result += spaces + "}";
            return result;
        }
    }
    return "unknown";
}

} // namespace lddk
